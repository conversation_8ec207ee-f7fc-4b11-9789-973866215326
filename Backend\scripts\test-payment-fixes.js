/**
 * Test script to verify payment fixes
 * This script tests the payment flow improvements to prevent the Stripe error:
 * "This PaymentMethod was previously used without being attached to a Customer"
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const Order = require('../models/Order');
const User = require('../models/User');
const Card = require('../models/Card');
const Payment = require('../models/Payment');

// Connect to database
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI || 'mongodb://localhost:27017/xosportshub';
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB connected for testing');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Test functions
const testPaymentMethodReuse = async () => {
  console.log('\n🧪 Testing Payment Method Reuse Prevention...');

  try {
    // Find an order with a payment intent
    const orderWithPaymentIntent = await Order.findOne({
      paymentIntentId: { $exists: true, $ne: null }
    }).populate('buyer');

    if (!orderWithPaymentIntent) {
      console.log('⚠️  No orders with payment intents found for testing');
      return;
    }

    console.log(`📋 Found test order: ${orderWithPaymentIntent._id}`);
    console.log(`💳 Payment Intent ID: ${orderWithPaymentIntent.paymentIntentId}`);
    console.log(`👤 Buyer: ${orderWithPaymentIntent.buyer.email}`);

    // Check if buyer has any cards marked as non-reusable
    const nonReusableCards = await Card.find({
      user: orderWithPaymentIntent.buyer._id,
      isReusable: false
    });

    console.log(`🚫 Non-reusable cards found: ${nonReusableCards.length}`);

    if (nonReusableCards.length > 0) {
      nonReusableCards.forEach(card => {
        console.log(`   - Card ending in ${card.lastFourDigits} (${card.cardType}) - Non-reusable`);
      });
    }

    // Check reusable cards
    const reusableCards = await Card.find({
      user: orderWithPaymentIntent.buyer._id,
      isReusable: { $ne: false }
    });

    console.log(`✅ Reusable cards found: ${reusableCards.length}`);

    if (reusableCards.length > 0) {
      reusableCards.forEach(card => {
        console.log(`   - Card ending in ${card.lastFourDigits} (${card.cardType}) - Reusable`);
      });
    }

  } catch (error) {
    console.error('❌ Error testing payment method reuse:', error);
  }
};

const testOrderPaymentStatus = async () => {
  console.log('\n🧪 Testing Order Payment Status Checks...');

  try {
    // Find orders with different payment statuses
    const pendingOrders = await Order.countDocuments({ paymentStatus: 'Pending' });
    const completedOrders = await Order.countDocuments({ paymentStatus: 'Completed' });
    const failedOrders = await Order.countDocuments({ paymentStatus: 'Failed' });

    console.log(`📊 Order Payment Status Summary:`);
    console.log(`   - Pending: ${pendingOrders}`);
    console.log(`   - Completed: ${completedOrders}`);
    console.log(`   - Failed: ${failedOrders}`);

    // Find orders that might have duplicate payment intents
    const ordersWithPaymentIntents = await Order.find({
      paymentIntentId: { $exists: true, $ne: null }
    }).select('_id paymentIntentId paymentStatus');

    const paymentIntentCounts = {};
    ordersWithPaymentIntents.forEach(order => {
      const intentId = order.paymentIntentId;
      if (!paymentIntentCounts[intentId]) {
        paymentIntentCounts[intentId] = [];
      }
      paymentIntentCounts[intentId].push(order);
    });

    const duplicateIntents = Object.entries(paymentIntentCounts)
      .filter(([intentId, orders]) => orders.length > 1);

    if (duplicateIntents.length > 0) {
      console.log(`⚠️  Found ${duplicateIntents.length} duplicate payment intents:`);
      duplicateIntents.forEach(([intentId, orders]) => {
        console.log(`   - Intent ${intentId}: ${orders.length} orders`);
        orders.forEach(order => {
          console.log(`     * Order ${order._id} - Status: ${order.paymentStatus}`);
        });
      });
    } else {
      console.log('✅ No duplicate payment intents found');
    }

  } catch (error) {
    console.error('❌ Error testing order payment status:', error);
  }
};

const testCardFingerprints = async () => {
  console.log('\n🧪 Testing Card Fingerprint Duplicates...');

  try {
    // Find cards with duplicate fingerprints for the same user
    const duplicateFingerprints = await Card.aggregate([
      {
        $group: {
          _id: { user: '$user', fingerprint: '$fingerprint' },
          cards: { $push: '$$ROOT' },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      }
    ]);

    if (duplicateFingerprints.length > 0) {
      console.log(`⚠️  Found ${duplicateFingerprints.length} users with duplicate card fingerprints:`);

      for (const duplicate of duplicateFingerprints) {
        const user = await User.findById(duplicate._id.user).select('email firstName lastName');
        console.log(`   - User: ${user.email} (${user.firstName} ${user.lastName})`);
        console.log(`     Fingerprint: ${duplicate._id.fingerprint}`);
        console.log(`     Cards: ${duplicate.count}`);

        duplicate.cards.forEach(card => {
          console.log(`       * Card ending in ${card.lastFourDigits} - Reusable: ${card.isReusable !== false ? 'Yes' : 'No'}`);
        });
      }
    } else {
      console.log('✅ No duplicate card fingerprints found');
    }

  } catch (error) {
    console.error('❌ Error testing card fingerprints:', error);
  }
};

const runTests = async () => {
  console.log('🚀 Starting Payment Fixes Test Suite...');
  console.log('='.repeat(50));

  await connectDB();

  await testPaymentMethodReuse();
  await testOrderPaymentStatus();
  await testCardFingerprints();

  console.log('\n' + '='.repeat(50));
  console.log('✅ Payment Fixes Test Suite Completed');

  // Close database connection
  await mongoose.connection.close();
  console.log('📝 Database connection closed');
};

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testPaymentMethodReuse,
  testOrderPaymentStatus,
  testCardFingerprints,
  runTests
};
